
#ifndef __CORE_PREREQUISITES_H__
#define __CORE_PREREQUISITES_H__

#include "OgrePrerequisites.h"

#define SANDBOX_SDK_VERSION_MAJOR 1
#define SANDBOX_SDK_VERSION_MINOR 32
#define SANDBOX_SDK_VERSION_PATCH 1
// �ð汾��Ӧ��svn�汾��
#define SANDBOX_SDK_SVN_LOG_NUMBER  12333

#define SDKTOSTRING2(arg) #arg
#define SANDBOX_SDK_VER_STR  SDKTOSTRING2(SANDBOX_SDK_VERSION_MAJOR) "." SDKTOSTRING2(SANDBOX_SDK_VERSION_MINOR) "." SDKTOSTRING2(SANDBOX_SDK_VERSION_PATCH)

// �������������Ż���ش����־λ by chenshaobin 2023/03/16
// #define UGC_PROFILE_ENABLED 

#ifdef __PC_LINUX__   //linux�������汾
	#define IWORLD_SERVER_BUILD    //�Ƿ��Ƿ����������汾
	#define IWORLD_TARGET_MOBILE   //Ŀ���ǹ����ֻ���
#else
	#if OGRE_PLATFORM==OGRE_PLATFORM_WIN32
		#define IWORLD_TARGET_PC   //Ŀ���ǹ���pc��
		//#define IWORLD_TARGET_MOBILE  //Ŀ���ǹ����ֻ���
		//#define IWORLD_TARGET_LIB    //���������õĿ��������Ϸ
	#else
		#define IWORLD_TARGET_MOBILE  //Ŀ���ǹ����ֻ���
	#endif
	
	//�Ƿ�Ϊ����״̬�������汾��ʱ��Ҫע�͵�����
	//#define IWORLD_DEV_BUILD���ĳ���CMAKE�Զ�����
	//
	//	#ifdef IWORLD_DEV_BUILD
	//	//�Ƿ���Ҫ����������obj, ���а�ע��
	//	//#define IWORLD_EXPOBJ_TOOLS
	//	#endif
#endif	 //__PC_LINUX__

#endif   //__CORE_PREREQUISITES_H__
